/**
 * 调试控制器 - 动态控制调试输出
 * 🎯 目标：提供便捷的调试开关，避免控制台垃圾邮件
 * 🔧 使用方法：在浏览器控制台中使用 window.debugController
 */

// 全局调试控制器接口
interface DebugController {
  // 快速开关
  enableAll(): void;
  disableAll(): void;
  enableCoordinateLogging(): void;
  disableCoordinateLogging(): void;
  enableRenderLogging(): void;
  disableRenderLogging(): void;
  enableColorLogging(): void;
  disableColorLogging(): void;
  enableButtonLogging(): void;
  disableButtonLogging(): void;
  enableStateLogging(): void;
  disableStateLogging(): void;
  
  // 状态查看
  getStatus(): any;
  
  // 帮助信息
  help(): void;
}

// 创建调试控制器
const createDebugController = (): DebugController => {
  return {
    // 启用所有调试输出
    enableAll() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        const config = (window as any).stores.debugConfig;
        config.enableCoordinateLogging = true;
        config.enableRenderLogging = true;
        config.enableColorLogging = true;
        config.enableStoreLogging = true;
        config.logButtonClicks = true;
        config.logStateChanges = true;
        console.log('✅ 已启用所有调试输出');
        this.getStatus();
      } else {
        console.warn('⚠️ 调试配置未找到，请确保应用已加载');
      }
    },

    // 禁用所有调试输出
    disableAll() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        const config = (window as any).stores.debugConfig;
        config.enableCoordinateLogging = false;
        config.enableRenderLogging = false;
        config.enableColorLogging = false;
        config.enableStoreLogging = false;
        config.logButtonClicks = false;
        config.logStateChanges = false;
        console.log('🔇 已禁用所有调试输出');
        this.getStatus();
      } else {
        console.warn('⚠️ 调试配置未找到，请确保应用已加载');
      }
    },

    // 启用坐标日志
    enableCoordinateLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableCoordinateLogging = true;
        console.log('📍 已启用坐标日志输出');
      }
    },

    // 禁用坐标日志
    disableCoordinateLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableCoordinateLogging = false;
        console.log('🔇 已禁用坐标日志输出');
      }
    },

    // 启用渲染日志
    enableRenderLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableRenderLogging = true;
        console.log('🎨 已启用渲染日志输出');
      }
    },

    // 禁用渲染日志
    disableRenderLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableRenderLogging = false;
        console.log('🔇 已禁用渲染日志输出');
      }
    },

    // 启用颜色日志
    enableColorLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableColorLogging = true;
        console.log('🌈 已启用颜色日志输出');
      }
    },

    // 禁用颜色日志
    disableColorLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.enableColorLogging = false;
        console.log('🔇 已禁用颜色日志输出');
      }
    },

    // 启用按钮日志
    enableButtonLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.logButtonClicks = true;
        console.log('🔘 已启用按钮日志输出');
      }
    },

    // 禁用按钮日志
    disableButtonLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.logButtonClicks = false;
        console.log('🔇 已禁用按钮日志输出');
      }
    },

    // 启用状态日志
    enableStateLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.logStateChanges = true;
        console.log('📊 已启用状态变化日志输出');
      }
    },

    // 禁用状态日志
    disableStateLogging() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        (window as any).stores.debugConfig.logStateChanges = false;
        console.log('🔇 已禁用状态变化日志输出');
      }
    },

    // 获取当前状态
    getStatus() {
      if (typeof window !== 'undefined' && (window as any).stores?.debugConfig) {
        const config = (window as any).stores.debugConfig;
        console.log('🔍 当前调试配置状态:');
        console.table({
          '总开关': config.enabled ? '✅ 开启' : '❌ 关闭',
          '坐标日志': config.enableCoordinateLogging ? '✅ 开启' : '❌ 关闭',
          '渲染日志': config.enableRenderLogging ? '✅ 开启' : '❌ 关闭',
          '颜色日志': config.enableColorLogging ? '✅ 开启' : '❌ 关闭',
          '按钮日志': config.logButtonClicks ? '✅ 开启' : '❌ 关闭',
          '状态日志': config.logStateChanges ? '✅ 开启' : '❌ 关闭',
          'Store日志': config.enableStoreLogging ? '✅ 开启' : '❌ 关闭',
          '特定坐标': config.logSpecificCoords.join(', '),
          '特定颜色': config.logSpecificColors.join(', '),
          '最大日志数': config.maxLogsPerRender
        });
        return config;
      } else {
        console.warn('⚠️ 调试配置未找到，请确保应用已加载');
        return null;
      }
    },

    // 显示帮助信息
    help() {
      console.log('🆘 调试控制器使用指南:');
      console.log('');
      console.log('🔧 快速开关:');
      console.log('  debugController.enableAll()              - 启用所有调试输出');
      console.log('  debugController.disableAll()             - 禁用所有调试输出');
      console.log('');
      console.log('📍 坐标日志控制:');
      console.log('  debugController.enableCoordinateLogging() - 启用坐标日志');
      console.log('  debugController.disableCoordinateLogging() - 禁用坐标日志');
      console.log('');
      console.log('🎨 渲染日志控制:');
      console.log('  debugController.enableRenderLogging()    - 启用渲染日志');
      console.log('  debugController.disableRenderLogging()   - 禁用渲染日志');
      console.log('');
      console.log('🌈 颜色日志控制:');
      console.log('  debugController.enableColorLogging()     - 启用颜色日志');
      console.log('  debugController.disableColorLogging()    - 禁用颜色日志');
      console.log('');
      console.log('🔘 按钮日志控制:');
      console.log('  debugController.enableButtonLogging()    - 启用按钮日志');
      console.log('  debugController.disableButtonLogging()   - 禁用按钮日志');
      console.log('');
      console.log('📊 状态日志控制:');
      console.log('  debugController.enableStateLogging()     - 启用状态变化日志');
      console.log('  debugController.disableStateLogging()    - 禁用状态变化日志');
      console.log('');
      console.log('📊 状态查看:');
      console.log('  debugController.getStatus()              - 查看当前配置状态');
      console.log('');
      console.log('💡 提示: 默认情况下所有调试输出都是关闭的，以避免控制台垃圾邮件');
    }
  };
};

// 导出调试控制器
export const debugController = createDebugController();

// 在浏览器环境中挂载到window对象
if (typeof window !== 'undefined') {
  (window as any).debugController = debugController;
}

// 声明全局类型
declare global {
  interface Window {
    debugController: DebugController;
  }
}
