/**
 * 调试控制器功能测试脚本
 * 🧪 用于验证调试输出控制是否正常工作
 * 
 * 使用方法：
 * 1. 在浏览器中打开应用
 * 2. 打开开发者工具控制台
 * 3. 复制粘贴此脚本并运行
 */

function testDebugController() {
    console.log('🧪 开始测试调试控制器功能...');
    console.log('='.repeat(50));
    
    // 检查基础环境
    if (!window.debugController) {
        console.error('❌ debugController未找到，请确保应用已加载');
        return;
    }
    
    if (!window.stores) {
        console.error('❌ window.stores未找到，请确保应用已加载');
        return;
    }
    
    console.log('✅ 基础环境检查通过');
    
    // 1. 测试状态查看
    console.log('\n📊 1. 测试状态查看功能...');
    const initialStatus = debugController.getStatus();
    
    if (initialStatus) {
        console.log('✅ 状态查看功能正常');
        
        // 验证默认配置（应该都是关闭的）
        const expectedDefaults = {
            enableCoordinateLogging: false,
            enableRenderLogging: false,
            enableColorLogging: false,
            enableStoreLogging: false
        };
        
        let defaultsCorrect = true;
        for (const [key, expected] of Object.entries(expectedDefaults)) {
            if (initialStatus[key] !== expected) {
                console.warn(`⚠️ ${key} 默认值不正确: 期望 ${expected}, 实际 ${initialStatus[key]}`);
                defaultsCorrect = false;
            }
        }
        
        if (defaultsCorrect) {
            console.log('✅ 默认配置正确 - 所有调试输出都已关闭');
        }
    } else {
        console.error('❌ 状态查看功能异常');
        return;
    }
    
    // 2. 测试启用功能
    console.log('\n🔛 2. 测试启用调试输出功能...');
    
    // 启用渲染日志
    debugController.enableRenderLogging();
    let status = debugController.getStatus();
    if (status && status.enableRenderLogging) {
        console.log('✅ 渲染日志启用功能正常');
    } else {
        console.error('❌ 渲染日志启用功能异常');
    }
    
    // 启用颜色日志
    debugController.enableColorLogging();
    status = debugController.getStatus();
    if (status && status.enableColorLogging) {
        console.log('✅ 颜色日志启用功能正常');
    } else {
        console.error('❌ 颜色日志启用功能异常');
    }
    
    // 3. 测试全部启用
    console.log('\n🔛 3. 测试全部启用功能...');
    debugController.enableAll();
    status = debugController.getStatus();
    
    if (status && 
        status.enableCoordinateLogging && 
        status.enableRenderLogging && 
        status.enableColorLogging && 
        status.enableStoreLogging) {
        console.log('✅ 全部启用功能正常');
    } else {
        console.error('❌ 全部启用功能异常');
    }
    
    // 4. 测试全部禁用
    console.log('\n🔇 4. 测试全部禁用功能...');
    debugController.disableAll();
    status = debugController.getStatus();
    
    if (status && 
        !status.enableCoordinateLogging && 
        !status.enableRenderLogging && 
        !status.enableColorLogging && 
        !status.enableStoreLogging) {
        console.log('✅ 全部禁用功能正常');
    } else {
        console.error('❌ 全部禁用功能异常');
    }
    
    // 5. 测试帮助功能
    console.log('\n📖 5. 测试帮助功能...');
    try {
        debugController.help();
        console.log('✅ 帮助功能正常');
    } catch (error) {
        console.error('❌ 帮助功能异常:', error);
    }
    
    // 6. 模拟用户操作测试
    console.log('\n🎮 6. 模拟用户操作测试...');
    
    // 查找红色按钮
    const redButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('红1') || btn.textContent?.includes('R1') || btn.textContent?.includes('red')
    );
    
    if (redButton) {
        console.log('✅ 找到红色按钮，准备测试调试输出控制...');
        
        // 先确保调试输出关闭
        debugController.disableAll();
        console.log('🔇 已关闭所有调试输出');
        
        // 点击按钮（应该没有调试输出）
        console.log('🖱️ 点击按钮（调试输出关闭状态）...');
        redButton.click();
        
        // 等待一下，然后启用调试输出再点击
        setTimeout(() => {
            console.log('🔛 启用渲染日志后再次点击...');
            debugController.enableRenderLogging();
            redButton.click();
            
            // 最后关闭调试输出
            setTimeout(() => {
                debugController.disableAll();
                console.log('🔇 测试完成，已关闭所有调试输出');
                
                console.log('\n' + '='.repeat(50));
                console.log('✅ 调试控制器功能测试完成！');
                console.log('💡 现在可以根据需要使用 debugController 控制调试输出');
            }, 1000);
        }, 1000);
        
    } else {
        console.warn('⚠️ 未找到红色按钮，跳过用户操作测试');
        
        console.log('\n' + '='.repeat(50));
        console.log('✅ 调试控制器基础功能测试完成！');
        console.log('💡 现在可以根据需要使用 debugController 控制调试输出');
    }
}

// 运行测试
testDebugController();
