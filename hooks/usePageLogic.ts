import { useMemo, useCallback, useEffect } from 'react';
import type { CellData } from '../types/grid';
import type { ColorType } from '../stores';
import type { ControlPanelContainerProps } from '../components/ControlPanel/types';

// 新Store架构导入
import { useBasicDataStore } from '../stores/basicDataStore';
import { useDynamicStyleStore } from '../stores/dynamicStyleStore';
import { useBusinessDataStore } from '../stores/businessDataStore';
import { useStyleStore } from '../stores/styleStore';

import { ColorCoordinateIndex } from '../utils/colorSystem';
import { usePerformanceOptimized } from './useFormHandlers';
import '../utils/debugController'; // 导入调试控制器，自动挂载到window对象

export const usePageLogic = () => {
    // 0. 性能优化hooks
    const {
        colorPriority,
        colorMap,
        baseClasses,
        defaultStyles,
        circleScaleConfig,
        getEffectiveColor,
        isLevelVisible,
        buildCellClasses,
        getCellKey,
        ensureSetType,
        checkLevelExists,
        debugCellRenderPath,
    } = usePerformanceOptimized();

    // 1. 从新Store架构获取状态和操作
    
    // 基础数据：网格数据、颜色坐标和可见性
    const basicDataStore = useBasicDataStore();
    const { 
        gridData, 
        initializeGrid,
        colorCoordinates,
        colorVisibility,
        setColorCoordinates,
        setColorVisibility,
        toggleColorLevel,
    } = basicDataStore;
    
    // 动态样式数据
    const dynamicStyleStore = useDynamicStyleStore();
    const {
        fontSize,
        matrixMargin,
        cellShape,
        displayMode,
        enableCircleScale,
        circleScaleFactor,
        showAllNumbers,
        showAllColors,
        showAllLevel1,
        showAllLevel2,
        showAllLevel3,
        showAllLevel4,
        setFontSize,
        setMatrixMargin,
        setCellShape,
        setDisplayMode,
        toggleCircleScale,
        setCircleScaleFactor,
        setShowAllNumbers,
        setShowAllColors,
        setShowAllLevel1,
        setShowAllLevel2,
        setShowAllLevel3,
        setShowAllLevel4,
    } = dynamicStyleStore;

    // 业务数据：交互状态、UI状态、版本管理
    const businessDataStore = useBusinessDataStore();
    const {
        clickedCells,
        handleCellClick,
        clearAllClickedCells,
        interactionState,
        setHoverInfo,
        activePanel,
        settingsSubPage,
        singleColorMode,
        showSpecificGroup,
        setActivePanel,
        setSettingsSubPage,
        setSingleColorMode,
        setShowSpecificGroup,
        showToastNotification,
        toastState,
        // 版本管理
        defaultVersions,
        currentDefaultVersion,
        newDefaultVersionName,
        groupModeVersions,
        currentGroupModeVersion,
        newGroupModeVersionName,
        mixedModeVersions,
        currentMixedModeVersion,
        newMixedModeVersionName,
        saveVersion,
        switchToVersion,
        deleteVersion,
        setNewVersionName,
    } = businessDataStore;

    // 样式数据：黑色格子显示、CSS映射
    const styleStore = useStyleStore();
    const { 
        showBlackCells, 
        setShowBlackCells,
        getColorCSSMap, 
        getBlackCSS 
    } = styleStore;

    // 2. 初始化网格数据
    useEffect(() => {
        initializeGrid();
    }, [initializeGrid]);

    // 🔧 全局调试配置：控制所有调试输出
    const DEBUG_CONFIG = useMemo(() => ({
        enabled: process.env.NODE_ENV === 'development',
        enableCoordinateLogging: false, // 默认关闭坐标逐个输出
        enableRenderLogging: false, // 默认关闭渲染决策日志
        enableColorLogging: false, // 默认关闭颜色应用日志
        logSpecificCoords: ['8,0', '4,0', '2,0', '1,0'], // 只记录特定坐标
        logSpecificColors: ['red'], // 只记录特定颜色
        maxLogsPerRender: 3, // 每次渲染最大日志数
        enableStoreLogging: false, // 默认关闭Store挂载日志
    }), []);

    // 2.1 Debug Phase-4 修复: 挂载stores到window对象以便debugHelper访问
    useEffect(() => {
        if (typeof window !== 'undefined') {
            (window as any).stores = {
                basicData: {
                    gridData,
                    colorCoordinates,
                    colorVisibility,
                    colorLevelRules: true, // 模拟数据
                },
                business: {
                    clickedCells,
                    interactionState,
                    showSpecificGroup,
                    defaultVersions,
                    currentDefaultVersion,
                },
                dynamic: {
                    fontSize,
                    matrixMargin,
                    cellShape,
                    displayMode,
                    enableCircleScale,
                    circleScaleFactor,
                },
                style: {
                    showBlackCells,
                    colorCSSMap: getColorCSSMap,
                },
                // 为了兼容现有的debugHelper，也添加combination别名
                combination: {
                    selectedGroups: new Map(), // 模拟数据
                    modeActivation: false, // 模拟数据
                },
                // 添加调试配置到stores中，方便在控制台中动态调整
                debugConfig: DEBUG_CONFIG
            };

            // 只在启用Store日志时输出
            if (DEBUG_CONFIG.enableStoreLogging) {
                console.log('🔗 Stores已挂载到window对象，debugHelper现在可以访问stores状态');
            }
        }
    }, [gridData, colorCoordinates, colorVisibility, clickedCells, interactionState,
        showSpecificGroup, defaultVersions, currentDefaultVersion, fontSize, matrixMargin,
        cellShape, displayMode, enableCircleScale, circleScaleFactor, showBlackCells, getColorCSSMap, DEBUG_CONFIG]);
    
    // 2.2 清理可能损坏的localStorage数据（一次性运行）
    useEffect(() => {
        try {
            // 检查并清理可能损坏的business-data-store数据
            const storedData = localStorage.getItem('business-data-store');
            if (storedData) {
                const parsed = JSON.parse(storedData);
                if (parsed.state && parsed.state.clickedCells && !(parsed.state.clickedCells instanceof Array)) {
                    console.warn('发现损坏的localStorage数据，正在清理...');
                    localStorage.removeItem('business-data-store');
                    window.location.reload(); // 重新加载页面以获取干净的状态
                }
            }
        } catch (error) {
            console.warn('清理localStorage数据时出错:', error);
            localStorage.removeItem('business-data-store');
        }
    }, []); // 只在组件首次挂载时运行

    // 3. 创建颜色索引（记忆化）
    const colorIndex = useMemo(() => {
        return new ColorCoordinateIndex(colorCoordinates);
    }, [colorCoordinates]);

    // 4. 核心渲染函数（使用useCallback和性能优化）- Debug Phase-1.2 增强调试
    const getCellStyle = useCallback((cell: CellData): string => {
        const { x, y } = cell;
        const coordKey = getCellKey(x, y);
        
        // 使用优化的Set类型检查
        const safeClickedCells = ensureSetType(clickedCells);
        const isClicked = safeClickedCells.has(coordKey);
        const allColorInfo = colorIndex.getAllColorInfo(x, y);

        // Debug Phase-1.2: 调试特定坐标
        debugCellRenderPath(x, y, allColorInfo, 'getCellStyle');

        let bgColor = defaultStyles.bgColor;
        let textColor = defaultStyles.textColor;
        let borderColor = defaultStyles.borderColor;

        // 黑色格子处理
        if (allColorInfo.black) {
            if (showBlackCells || isClicked) {
                bgColor = getBlackCSS('bg') || 'bg-black';
                textColor = 'text-white';
                borderColor = 'border-transparent';
            }
        } else {
            // 使用优化的颜色查找函数
            const effectiveColor = getEffectiveColor(allColorInfo, colorPriority);

            if (singleColorMode && (!effectiveColor || effectiveColor.type !== singleColorMode)) {
                bgColor = defaultStyles.grayBg;
                textColor = defaultStyles.grayText;
                borderColor = defaultStyles.grayBorder;
            } else if (effectiveColor) {
                const { type, level, group } = effectiveColor;
                const visibility = colorVisibility[type as keyof typeof colorVisibility];
                
                // Debug-3.2: 更新级别可见性检查调用，传递colorType参数
                const debugContext = { coords: coordKey };
                const levelVisible = isLevelVisible(visibility, level, type, debugContext);
                const isGroupVisible = showSpecificGroup === null || group === showSpecificGroup;

                // 🔧 优化调试日志：使用全局调试配置控制输出
                const shouldLogRender = DEBUG_CONFIG.enabled &&
                    DEBUG_CONFIG.enableRenderLogging &&
                    DEBUG_CONFIG.logSpecificCoords.includes(coordKey) &&
                    (!DEBUG_CONFIG.logSpecificColors.length || DEBUG_CONFIG.logSpecificColors.includes(type));

                if (shouldLogRender) {
                    const finalCondition = (visibility?.showCells !== false && levelVisible && isGroupVisible) || isClicked;
                    console.log(`🎨 颜色渲染决策 ${coordKey}:`, {
                        colorType: type,
                        level,
                        group,
                        'visibility?.showCells': visibility?.showCells,
                        levelVisible,
                        isGroupVisible,
                        isClicked,
                        finalCondition,
                        '🔧优化': '仅记录关键坐标'
                    });

                    // 特别关注红色格子的渲染问题
                    console.log(`🔴 红色格子${coordKey}详细分析:`, {
                        level,
                        levelKey: `showLevel${level}`,
                        levelValue: visibility?.[`showLevel${level}` as keyof typeof visibility],
                        levelValueType: typeof visibility?.[`showLevel${level}` as keyof typeof visibility],
                        showCells: visibility?.showCells,
                        showCellsType: typeof visibility?.showCells,
                        levelVisible,
                        finalCondition,
                        willRender: finalCondition ? '✅ 将渲染颜色' : '❌ 将显示灰色'
                    });
                }

                if ((visibility?.showCells !== false && levelVisible && isGroupVisible) || isClicked) {
                    const levelClassKey = `level${level}` as 'level1' | 'level2' | 'level3' | 'level4';
                    const colorCSS = getColorCSSMap(type as ColorType);
                    const levelBgColor = colorCSS?.[levelClassKey];

                    // 🔧 修复：确保颜色CSS正确应用
                    if (levelBgColor) {
                        bgColor = levelBgColor;
                    } else if (colorCSS?.bg) {
                        bgColor = colorCSS.bg;
                    } else {
                        // 备用颜色映射
                        const fallbackColors: Record<string, string> = {
                            red: 'bg-red-500',
                            cyan: 'bg-cyan-500',
                            yellow: 'bg-yellow-500',
                            purple: 'bg-purple-500',
                            orange: 'bg-orange-500',
                            green: 'bg-green-500',
                            blue: 'bg-blue-500',
                            pink: 'bg-pink-500'
                        };
                        bgColor = fallbackColors[type] || 'bg-gray-500';
                    }

                    textColor = 'text-white';
                    borderColor = 'border-transparent';

                    // 🔧 优化调试：使用全局配置控制颜色应用日志
                    if (shouldLogRender && DEBUG_CONFIG.enableColorLogging) {
                        console.log(`✅ 应用${type}样式 ${coordKey}:`, {
                            level,
                            levelClassKey,
                            colorCSS,
                            levelBgColor,
                            finalBgColor: bgColor,
                            '🔧修复': '增强了颜色CSS映射'
                        });
                    }
                } else {
                    // 🔧 优化调试：使用全局配置控制未应用原因日志
                    if (shouldLogRender && DEBUG_CONFIG.enableColorLogging) {
                        console.warn(`❌ 未应用${type}样式 ${coordKey}:`, {
                            level,
                            'showCells': visibility?.showCells,
                            levelVisible,
                            isGroupVisible,
                            isClicked,
                            reason: !levelVisible ? 'level不可见' :
                                   !isGroupVisible ? 'group不可见' :
                                   visibility?.showCells === false ? 'showCells=false' : '未知原因'
                        });
                    }
                }
            }
        }

        // 使用优化的CSS类构建函数
        return buildCellClasses(baseClasses, cellShape, bgColor, textColor, borderColor, isClicked, defaultStyles);
    }, [clickedCells, colorIndex, cellShape, showBlackCells, getBlackCSS, singleColorMode, colorVisibility, showSpecificGroup, getColorCSSMap, getCellKey, ensureSetType, defaultStyles, getEffectiveColor, colorPriority, isLevelVisible, buildCellClasses, baseClasses, debugCellRenderPath]);

    // 5. 圆形缩放样式（使用useCallback和性能优化）
    const getCircleScaleStyle = useCallback((cell: CellData): React.CSSProperties => {
        if (!enableCircleScale) return {};
        
        const allColorInfo = colorIndex.getAllColorInfo(cell.x, cell.y);
        const { black, ...colors } = allColorInfo;

        if (black && showBlackCells) {
             return {
                transform: `scale(${circleScaleFactor})`,
                ...circleScaleConfig,
            };
        }

        for (const color in colors) {
            const colorInfo = colors[color as ColorType];
            if (colorInfo && colorInfo.level === 1) {
                const visibility = colorVisibility[color as keyof typeof colorVisibility];
                if ((visibility?.showCells !== false && visibility?.showLevel1 !== false)) {
                    return {
                        transform: `scale(${circleScaleFactor})`,
                        ...circleScaleConfig,
                    };
                }
            }
        }

        return {};
    }, [enableCircleScale, circleScaleFactor, colorIndex, showBlackCells, colorVisibility, circleScaleConfig]);

    // 6. 格子内容计算函数（使用useCallback和性能优化）
    const getCellContent = useCallback((cell: CellData) => {
        const { x, y, number } = cell;
        const allColorInfo = colorIndex.getAllColorInfo(x, y);

        if (showAllNumbers) {
            if (allColorInfo.black) return allColorInfo.black.letter;
            
            // 使用优化的颜色查找
            for (const color of colorPriority) {
                if (allColorInfo[color]) return colorMap[color];
            }
            return '';
        }

        if (displayMode === 'hidden') return '';
        if (displayMode === 'coordinate') return `${x},${y}`;
        
        if (allColorInfo.black) {
            if (showBlackCells) {
                if (displayMode === 'number') return number.toString();
                return allColorInfo.black.letter;
            }
        } else {
            // 使用优化的颜色查找函数
            const effectiveColor = getEffectiveColor(allColorInfo, colorPriority);

            if (singleColorMode && (!effectiveColor || effectiveColor.type !== singleColorMode)) return '';

            if (effectiveColor) {
                const { type, level, group } = effectiveColor;
                const visibility = colorVisibility[type as keyof typeof colorVisibility];
                const levelVisible = isLevelVisible(visibility, level, type);
                const isGroupVisible = showSpecificGroup === null || group === showSpecificGroup;

                if ((visibility?.showCells !== false && levelVisible && isGroupVisible)) {
                    if (displayMode === 'number') return number.toString();
                    return group?.toString() ?? '';
                }
            }
        }

        return '';
    }, [colorIndex, showAllNumbers, displayMode, showBlackCells, singleColorMode, colorVisibility, showSpecificGroup, colorPriority, colorMap, getEffectiveColor, isLevelVisible]);

    // 7. 处理格子点击（使用useCallback优化性能）
    const onCellClick = useCallback((cell: CellData) => {
        const cellKey = `${cell.x},${cell.y}`;
        handleCellClick(cellKey);
    }, [handleCellClick]);

    // 8. 处理悬停信息的适配器函数（使用useCallback优化性能）
    const handleHoverInfo = useCallback((info: string) => {
        setHoverInfo({
            x: 0,
            y: 0,
            content: info
        });
    }, [setHoverInfo]);

    // 9. Tab样式函数（使用useCallback优化性能）
    const getTabStyle = useCallback((tabKey: string, isActive: boolean) => {
        return `px-4 py-2 text-sm font-semibold border-b-2 transition-colors duration-200 ${
            isActive ? 'border-blue-500 text-white' : 'border-transparent text-gray-400 hover:text-white'
        }`;
    }, []);

    // 10. R2面板架构：如果是首次访问，默认显示R2样式面板
    const effectiveActivePanel = activePanel || 'r2-style';

    // 11. 构建控制面板Props（记忆化优化）
    const controlPanelProps: ControlPanelContainerProps = useMemo(() => ({
        activePanel: effectiveActivePanel,
        setActivePanel: (panel) => setActivePanel(panel as any),
        getTabStyle,
        versionProps: {
            currentVersion: currentDefaultVersion,
            onVersionChange: (version) => {
                const versionData = defaultVersions[version];
                if (versionData && versionData.data) {
                    // 恢复颜色坐标
                    if (versionData.data.colorCoordinates) {
                        Object.entries(versionData.data.colorCoordinates).forEach(([color, coordinates]) => {
                            setColorCoordinates(color as keyof typeof colorCoordinates, coordinates as any);
                        });
                    }
                    // 恢复颜色可见性
                    if (versionData.data.colorVisibility) {
                        Object.entries(versionData.data.colorVisibility).forEach(([color, visibility]) => {
                            setColorVisibility(color as keyof typeof colorVisibility, visibility as any);
                        });
                    }
                }
                switchToVersion('default', version);
            },
            versions: Object.keys(defaultVersions).map(name => ({
                id: name,
                name,
                description: defaultVersions[name]?.description || ''
            })),
            onSaveVersion: () => {
                if (newDefaultVersionName.trim()) {
                    saveVersion('default', newDefaultVersionName, {
                        colorCoordinates,
                        colorVisibility
                    });
                    showToastNotification(`版本 "${newDefaultVersionName}" 已保存`, 'success');
                }
            },
            onDeleteVersion: (versionName) => {
                deleteVersion('default', versionName);
                showToastNotification(`版本 "${versionName}" 已删除`, 'success');
            },
            onExportData: () => {
                const dataStr = JSON.stringify(defaultVersions, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `版本数据_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                URL.revokeObjectURL(url);
                showToastNotification('数据导出成功！', 'success');
            },
            onImportData: (event) => {
                const file = event.target.files?.[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target?.result as string);
                        showToastNotification('数据导入成功！', 'success');
                    } catch (error) {
                        showToastNotification('导入失败，请检查文件格式！', 'error');
                    }
                };
                reader.readAsText(file);
                event.target.value = '';
            },
        },
    }), [
        effectiveActivePanel, setActivePanel, getTabStyle, currentDefaultVersion, defaultVersions, 
        newDefaultVersionName, colorCoordinates, colorVisibility, setColorCoordinates, setColorVisibility, 
        switchToVersion, saveVersion, deleteVersion, showToastNotification
    ]);

    // 返回所有页面需要的状态和函数
    return {
        // 网格数据
        gridData,
        colorIndex,
        
        // 样式配置
        fontSize,
        matrixMargin,
        cellShape,
        displayMode,
        
        // 核心渲染函数
        getCellStyle,
        getCellContent,
        getCircleScaleStyle,
        
        // 事件处理函数
        onCellClick,
        handleHoverInfo,
        
        // UI状态
        interactionState,
        toastState,
        effectiveActivePanel,
        
        // 组件Props
        controlPanelProps,
        
        // Toast状态解构
        showToast: toastState.show,
        toastMessage: toastState.message,
        toastType: toastState.type,
    };
}; 